import React, { useState, useEffect, useCallback } from 'react';
import { Filter, UserCircle, ChevronLeft, ChevronRight, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import { Profile } from '../types';
import { browseProfiles } from '../lib/profile';
import { useAuth } from '../hooks/useAuth';
import { useConversation } from '../context/ConversationContext';
import { useToast } from '../hooks/useToast';
import ProfileCard from '../components/discovery/ProfileCard';
import DetailedProfileView from '../components/discovery/DetailedProfileView';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import styles from './Discover.module.css';

/**
 * @file Discover.tsx
 * @description Page component for discovering other user profiles.
 * Allows users to filter profiles based on criteria like age and gender,
 * view profiles in a card layout, see detailed profile views, and paginate through results.
 */

const Discover: React.FC = () => {
  const { user } = useAuth(); // Current authenticated user
  const { startConversation } = useConversation(); // Context function to start a new conversation
  const navigate = useNavigate(); // For programmatic navigation
  const { showSuccess, showError, showLoading, dismiss } = useToast(); // Toast notifications

  // State for filter input fields (unapplied filters)
  const [filters, setFilters] = useState({ ageMin: '', ageMax: '', gender: '' });
  // State for filters that are currently applied to the search results
  const [appliedFilters, setAppliedFilters] = useState({ ageMin: '', ageMax: '', gender: '' });
  // State to store the array of fetched profiles
  const [profiles, setProfiles] = useState<Profile[]>([]);
  // State for the total number of profiles matching the current filters (for pagination)
  const [totalProfiles, setTotalProfiles] = useState(0);
  // State for the current page number in pagination
  const [currentPage, setCurrentPage] = useState(1);
  // Constant for the number of profiles to display per page
  const [pageSize] = useState(9);
  // State to indicate if profiles are currently being fetched
  const [isLoading, setIsLoading] = useState(false);
  // State to store any error message during profile fetching
  const [error, setError] = useState<string | null>(null);
  // State for the profile currently selected to be viewed in detail
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  // State to control the visibility of the DetailedProfileView modal
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false);

  /**
   * Fetches profiles from the backend based on current filters and pagination.
   * Updates component state with fetched profiles, total count, loading, and error states.
   * @param page - The page number to fetch.
   * @param currentFilters - The filters to apply to the profile search.
   */
  const fetchProfiles = useCallback(async (page: number, currentFilters: typeof appliedFilters) => {
    if (!user?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const parsedFilters = {
        ageMin: currentFilters.ageMin ? parseInt(currentFilters.ageMin, 10) : undefined,
        ageMax: currentFilters.ageMax ? parseInt(currentFilters.ageMax, 10) : undefined,
        gender: currentFilters.gender || undefined,
      };
      const result = await browseProfiles(parsedFilters, { page, pageSize }, user.id);
      if (result) {
        setProfiles(result.profiles);
        setTotalProfiles(result.totalCount);
      } else {
        setError('Failed to load profiles. Please try again.');
        setProfiles([]);
        setTotalProfiles(0);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error(err);
      setProfiles([]);
      setTotalProfiles(0);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, pageSize]);

  useEffect(() => {
    // This effect triggers `fetchProfiles` whenever the current page or applied filters change.
    fetchProfiles(currentPage, appliedFilters);
  }, [currentPage, appliedFilters, fetchProfiles]);

  // Handles changes in filter input fields.
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  /**
   * Applies the current filter values to the search.
   * Resets to page 1 and updates `appliedFilters` state, which triggers `fetchProfiles`.
   */
  const handleApplyFilters = () => {
    setCurrentPage(1); // Reset to first page when filters change
    setAppliedFilters(filters);
  };

  // Clears all filter inputs and applied filters, then re-fetches profiles.
  const handleClearFilters = () => {
    setFilters({ ageMin: '', ageMax: '', gender: '' });
    setCurrentPage(1); // Reset to first page
    setAppliedFilters({ ageMin: '', ageMax: '', gender: '' });
  };

  /**
   * Handles the click event on a profile card.
   * Sets the selected profile and opens the detailed profile view modal.
   * @param profile - The profile object that was clicked.
   */
  const handleProfileCardClick = (profile: Profile) => {
    setSelectedProfile(profile);
    setIsDetailViewOpen(true);
  };

  // Closes the detailed profile view modal.
  const handleCloseDetailView = () => {
    setIsDetailViewOpen(false);
    setSelectedProfile(null);
  };

  /**
   * Handles the action to start a chat with a selected profile.
   * Uses `startConversation` from context, shows toasts for feedback,
   * and navigates to the conversation page on success.
   * @param profileToChat - The profile object of the user to start a chat with.
   */
  const handleStartChat = async (profileToChat: Profile) => {
    if (!user) {
      showError("Please log in to start a chat.");
      return;
    }
    const loadingToastId = showLoading(`Starting chat with ${profileToChat.name}...`);
    try {
      const newConversation = await startConversation(profileToChat, 'real_user');
      dismiss(loadingToastId);
      if (newConversation && newConversation.id) {
        showSuccess(`Chat started with ${profileToChat.name}!`);
        navigate(`/conversations/${newConversation.id}`);
      } else {
        showError(`Failed to start chat with ${profileToChat.name}.`);
      }
    } catch (err) {
      dismiss(loadingToastId);
      showError(`Error starting chat: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error("Error starting chat:", err);
    }
    handleCloseDetailView();
  };
  
  const totalPages = Math.ceil(totalProfiles / pageSize);

  return (
    <div className={styles.container}>
      {/* Main Title */}
      <div className={styles.header}>
        <Search className={styles.headerIcon} />
        <h1 className={styles.headerTitle}>Discover Connections</h1>
      </div>

      {/* Filter Bar Area */}
      <div className={styles.filterSection}>
        <div className={styles.filterHeader}>
          <Filter className={styles.filterIcon} />
          <h2 className={styles.filterTitle}>Filters</h2>
        </div>
        <div className={styles.filterGrid}>
          <div>
            <label htmlFor="ageMin" className="block text-sm font-medium text-gray-700 mb-1">
              Age Range (Min)
            </label>
            <input
              type="number"
              id="ageMin"
              name="ageMin"
              value={filters.ageMin}
              onChange={handleFilterChange}
              placeholder="18"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
          <div>
            <label htmlFor="ageMax" className="block text-sm font-medium text-gray-700 mb-1">
              Age Range (Max)
            </label>
            <input
              type="number"
              id="ageMax"
              name="ageMax"
              value={filters.ageMax}
              onChange={handleFilterChange}
              placeholder="99"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
          <div>
            <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
              Gender
            </label>
            <select
              id="gender"
              name="gender"
              value={filters.gender}
              onChange={handleFilterChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
            >
              <option value="">Any</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="non-binary">Non-binary</option>
              <option value="other">Other</option>
              <option value="prefer_not_to_say">Prefer not to say</option>
            </select>
          </div>
        </div>
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleClearFilters}
            className="px-5 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Clear Filters
          </button>
          <button
            type="button"
            onClick={handleApplyFilters}
            className="px-5 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Apply Filters
          </button>
        </div>
      </div>      {/* Results Area */}
      <div className="mb-8">
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner />
            <span className="ml-3 text-gray-600">Loading profiles...</span>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center py-12 text-red-600">
            <p>{error}</p>
          </div>
        ) : profiles.length === 0 ? (
          <div className="flex flex-col justify-center items-center py-12 text-gray-500">
            <UserCircle className="w-16 h-16 mb-4 text-gray-400" />
            <p className="text-lg">No profiles found matching your criteria.</p>
            <p className="text-sm mt-2">Try adjusting your filters or clearing them.</p>
          </div>
        ) : (
          <div className={styles.profileGrid}>
            {profiles.map((profile) => (
              <div key={profile.id} className={styles.profileCardWrapper}>
                <ProfileCard
                  profile={profile}
                  onClick={() => handleProfileCardClick(profile)}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination Area */}
      {!isLoading && !error && profiles.length > 0 && (
        <div className="flex items-center justify-center space-x-4 p-4 bg-white rounded-xl shadow-lg">
          <button
            type="button"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous
          </button>
          <span className="text-sm text-gray-700">Page {currentPage} of {totalPages}</span>
          <button
            type="button"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      )}

      {/* DetailedProfileView Modal */}
      {isDetailViewOpen && selectedProfile && (
        <DetailedProfileView
          profile={selectedProfile}
          onStartChat={handleStartChat}
          onClose={handleCloseDetailView}
        />
      )}
    </div>
  );
};

export default Discover;
