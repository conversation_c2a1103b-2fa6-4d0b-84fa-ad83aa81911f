import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './components/auth/AuthProvider';
import { ProfileProvider } from './context/ProfileContext';
import { ConversationProvider } from './context/ConversationContext';
import { AuthGuard } from './components/auth/AuthGuard';
import { LoginPage } from './components/auth/LoginPage';
import { AuthCallback } from './pages/AuthCallback';
import { Header } from './components/layout/Header';
import { Footer } from './components/layout/Footer';
import { Toast } from './components/ui/Toast';
import Dashboard from './pages/Dashboard';
import ProfileCreation from './pages/ProfileCreation';
import Conversations from './pages/Conversations';
import ConversationDetails from './components/conversation/ConversationDetails';
import Settings from './pages/Settings';
import Discover from './pages/Discover';
import TaggingDemo from './pages/TaggingDemo';
// Import debug utilities for conversation boundaries
import './utils/setupConversationBoundaries';
import PrivacyPolicy from './pages/PrivacyPolicy';
import NotFound from './pages/NotFound';
import './index.css';

function App() {
  return (
    <Router>
      <AuthProvider>
        <ProfileProvider>
          <ConversationProvider>
            <div className="flex flex-col min-h-screen bg-slate-50">
              <Header />
              <main className="flex-grow">
                <Routes>
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/auth/callback" element={<AuthCallback />} />
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route
                    path="/dashboard"
                    element={
                      <AuthGuard>
                        <Dashboard />
                      </AuthGuard>
                    }
                  />
                  <Route
                    path="/profile/create"
                    element={
                      <AuthGuard>
                        <ProfileCreation />
                      </AuthGuard>
                    }
                  />
                  <Route
                    path="/discover"
                    element={
                      <AuthGuard>
                        <Discover />
                      </AuthGuard>
                    }
                  />
                  <Route
                    path="/conversations"
                    element={
                      <AuthGuard>
                        <Conversations />
                      </AuthGuard>
                    }
                  />
                  <Route
                    path="/conversations/:id"
                    element={
                      <AuthGuard>
                        <ConversationDetails />
                      </AuthGuard>
                    }
                  />
                  <Route
                    path="/settings"
                    element={
                      <AuthGuard>
                        <Settings />
                      </AuthGuard>
                    }
                  />
                  <Route
                    path="/tagging-demo"
                    element={
                      <AuthGuard>
                        <TaggingDemo />
                      </AuthGuard>
                    }
                  />
                  <Route path="/privacy" element={<PrivacyPolicy />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>
              <Footer />
              <Toast />
            </div>
          </ConversationProvider>
        </ProfileProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;