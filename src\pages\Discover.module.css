/* Discover.module.css - Optimized grid layout for profile cards */

.container {
  @apply container mx-auto px-4 py-8;
}

.header {
  @apply flex items-center mb-8;
}

.headerIcon {
  @apply w-8 h-8 text-purple-600 mr-3;
}

.headerTitle {
  @apply text-3xl font-bold text-gray-800;
}

.filterSection {
  @apply mb-8 p-6 bg-white rounded-xl shadow-lg;
}

.filterHeader {
  @apply flex items-center mb-4;
}

.filterIcon {
  @apply w-6 h-6 text-purple-500 mr-2;
}

.filterTitle {
  @apply text-xl font-semibold text-gray-700;
}

.filterGrid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.filterField {
  @apply block;
}

.filterLabel {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.filterInput {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500;
}

.filterSelect {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500;
}

.filterActions {
  @apply flex justify-end space-x-4 mt-6;
}

.filterButton {
  @apply px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium;
}

.clearButton {
  @apply px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium;
}

.resultsSection {
  @apply mb-8;
}

.loadingContainer {
  @apply flex justify-center items-center py-12;
}

.loadingText {
  @apply ml-3 text-gray-600;
}

.errorContainer {
  @apply flex justify-center items-center py-12 text-red-600;
}

.emptyContainer {
  @apply flex flex-col justify-center items-center py-12 text-gray-500;
}

.emptyIcon {
  @apply w-16 h-16 mb-4 text-gray-400;
}

.emptyTitle {
  @apply text-lg;
}

.emptySubtitle {
  @apply text-sm mt-2;
}

/* Optimized Grid Layout */
.profileGrid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  align-items: stretch;
}

/* Responsive breakpoints for better control */
@media (min-width: 640px) {
  .profileGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .profileGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .profileGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .profileGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
  }
}

/* Ensure cards stretch to fill grid cells */
.profileCardWrapper {
  display: flex;
  width: 100%;
}

.profileCardWrapper > * {
  flex: 1;
}

/* Pagination */
.paginationContainer {
  @apply flex items-center justify-center space-x-4 p-4 bg-white rounded-xl shadow-lg;
}

.paginationButton {
  @apply px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 flex items-center disabled:opacity-50 disabled:cursor-not-allowed;
}

.paginationInfo {
  @apply text-sm text-gray-700;
}

.paginationIcon {
  @apply w-4 h-4;
}

.paginationIconLeft {
  @apply w-4 h-4 mr-1;
}

.paginationIconRight {
  @apply w-4 h-4 ml-1;
}

/* Improved spacing and alignment */
.section {
  @apply mb-8;
}

/* Focus and accessibility improvements */
.profileGrid:focus-within {
  outline: none;
}

/* Animation for grid items */
.profileCardWrapper {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for multiple cards */
.profileCardWrapper:nth-child(1) { animation-delay: 0ms; }
.profileCardWrapper:nth-child(2) { animation-delay: 100ms; }
.profileCardWrapper:nth-child(3) { animation-delay: 200ms; }
.profileCardWrapper:nth-child(4) { animation-delay: 300ms; }
.profileCardWrapper:nth-child(5) { animation-delay: 400ms; }
.profileCardWrapper:nth-child(6) { animation-delay: 500ms; }

/* Ensure consistent spacing on all screen sizes */
@media (max-width: 640px) {
  .container {
    @apply px-2 py-6;
  }
  
  .filterSection {
    @apply p-4;
  }
  
  .profileGrid {
    gap: 1rem;
    grid-template-columns: 1fr;
  }
}
